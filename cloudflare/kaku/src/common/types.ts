import { User } from '../user/User';
import { z } from 'zod';
import type { CoordinatorDO } from '../coordinator/coordinator-do';

export type KakuApp = {
  Bindings: Env;
  Variables: {
    user?: User;
    userId: string;
    linkId: string;
    serviceId: string;
    coordinatorStub: DurableObjectStub<CoordinatorDO>;
  };
};

export const TokenInfoSchema = z.object({
  xAuthToken: z.string(),
  csrfToken: z.string(),
});

export type TokenInfo = z.infer<typeof TokenInfoSchema>;
