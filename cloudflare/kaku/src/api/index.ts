import { <PERSON>o } from 'hono';
import { agentsMiddleware } from 'hono-agents';
import { <PERSON>ku<PERSON>pp } from '../common/types';
import { SimulatedLoginHandler } from './simulated-login-handler';
import { ConnectionsHandler, <PERSON>sHandler } from './handlers';

const app = new Hono<KakuApp>();

// Note: Agent class names are transformed to kebab-case in URLs
// Example: ConnectionAgent → /agents/connection-agent/[platformId]
// In our case, it's /agents/connections/[platformId]
app
  .use(
    '*',
    agentsMiddleware({
      options: {
        cors: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
        prefix: 'agents',
      },
      onError: (error) => {
        console.error('Agent middleware error:', error);
      },
    }),
  )
  .route('/connections', ConnectionsHandler)
  .route('/links', LinksHandler)
  .route('/v1/simulated', SimulatedLoginHandler);


export default app;

export { Connections } from '../agent/connection-agent';
export { CoordinatorDO } from '../coordinator/coordinator-do';
export { ConnectionsWorkflow } from '../workflow/connections-workflow';

