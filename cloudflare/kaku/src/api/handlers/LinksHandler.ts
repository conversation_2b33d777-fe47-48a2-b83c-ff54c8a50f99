import { Hono } from 'hono';
import { <PERSON>ku<PERSON>pp } from '../../common/types';
import { platformDetails, PlatformTypes } from '../../ui/constants';
import { SERVICE_ID, USER_ID } from '../constants';

const app = new Hono<KakuApp>();

app.post('/', async (c) => {
  const body = await c.req.json();
  const serviceId = body[SERVICE_ID] as PlatformTypes;
  const userId = body[USER_ID] as string; // TODO: this will be removed once we have proper authentication

  const platformDetails2 = platformDetails[serviceId];
  if (!platformDetails2) {
    return c.json({ error: 'Invalid serviceId' }, 400);
  }

  const coordinator = c.env.CoordinatorDO.idFromName(userId);
  const stub = c.env.CoordinatorDO.get(coordinator);
  const linkResponse = await stub.createLink(serviceId, userId);
  return c.json(linkResponse);
});

// internal api to get users links for debugging purposes
app.get('/internal/', async (c) => {
  const userId = c.req.query(USER_ID) as string; // TODO: this will be removed once we have proper authentication

  const coordinator = c.env.CoordinatorDO.idFromName(userId);
  const stub = c.env.CoordinatorDO.get(coordinator);
  const links = await stub.state;
  return c.json(links);
});

export { app as LinksHandler };
