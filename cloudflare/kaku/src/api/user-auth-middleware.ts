import { MiddlewareHandler } from 'hono';
import { <PERSON><PERSON><PERSON><PERSON> } from '../common/types';
import { User } from '../user/User';
import { USER } from './constants';

export const userAuthMiddleware: MiddlewareHandler<KakuApp> = async (c, next) => {
  // Extract headers from the current request
  const headers = new Headers(c.req.header());

  const hasAuthorizationHeader = headers.has('X-Auth-Token');

  const endpoint = hasAuthorizationHeader
    ? `${c.env.SUNNY_API_ENDPOINT}/v1/users`
    : `${c.env.SUNNY_API_ENDPOINT}/web/v1/users`;

  // Perform the fetch call to the determined endpoint with the same headers
  const userResponse = await fetch(endpoint, {
    method: 'GET',
    headers: headers,
  });
  if (userResponse.status === 401 || !userResponse.ok) {
    return c.json({ error: 'Unauthorized' }, 401);
  }
  const user = (await userResponse.json()) as User;
  c.set(USER, user);
  // If authorized, proceed to the next middleware or route handler
  await next();
};
