/**
 * HTMX Form Generator
 * Converts FormVisionResult schema to HTMX markup
 */

export type {
  FormVisionResult,
  FormMetadata,
  FormControls,
  FormField,
  FormButton,
} from './types/form-interfaces';

import {
  generateFormOpenTag,
  generateFormCloseTag,
  generatePromptDisplay,
  getErrorMessages,
  generateErrorMessages,
  generateFloatingLabelField,
  generateCheckboxField,
  generateRadioField,
  generateTextareaField,
  generateStandardField,
  generateFormButton,
} from './components';
import type { FormVisionResult, FormField } from './types/form-interfaces';

/**
 * Generates HTMX form markup from FormVisionResult
 */
export class HTMXFormGenerator {
  generateForm(formData: FormVisionResult): string {
    const { screenInfo, controls } = formData;

    const formElements: string[] = [];

    formElements.push(generateFormOpenTag());

    const errorMessages = getErrorMessages(screenInfo);

    // Add instruction prominently if present
    if (screenInfo.instruction && errorMessages.length === 0) {
      formElements.push(generatePromptDisplay(screenInfo.instruction, null));
    }

    if (errorMessages.length > 0) {
      formElements.push(generateErrorMessages(errorMessages));
    }

    // Generate form fields
    controls.fields.forEach((field) => {
      const fieldMarkup = this.generateFormField(field);
      if (fieldMarkup) {
        formElements.push(fieldMarkup);
      }
    });

    controls.buttons.forEach((button) => {
      formElements.push(generateFormButton(button));
    });

    formElements.push(generateFormCloseTag());

    return formElements.join('\n');
  }

  /**
   * Generate form field based on field type
   */
  private generateFormField(field: FormField): string | null {
    switch (field.fieldControlType) {
      case 'text':
      case 'password':
      case 'number':
        return generateFloatingLabelField(field);
      case 'select':
      case 'dropdown':
        return null;
      case 'checkbox':
        return generateCheckboxField(field);
      case 'checkboxgroup':
        return generateCheckboxField(field);
      case 'radio':
        return generateRadioField(field);
      case 'textarea':
        return generateTextareaField(field);
      default:
        return generateStandardField(field);
    }
  }
}

// Export singleton instance
export const htmxFormGenerator = new HTMXFormGenerator();
