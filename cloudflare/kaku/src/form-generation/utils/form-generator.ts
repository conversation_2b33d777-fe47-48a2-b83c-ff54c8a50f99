/**
 * Isolated form generation utilities
 */

import { htmxFormGenerator } from '../htmx-generator';
import { FormVisionResult } from '../types/form-interfaces';

/**
 * Generate HTMX form markup from FormVisionResult
 * This is an isolated function that can be called independently
 */
export function generateHtmxForm(extractionResult: FormVisionResult): string {
  return htmxFormGenerator.generateForm(extractionResult);
}
