import { env, runDurableObjectAlarm } from 'cloudflare:test';
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { CoordinatorDO } from '../../src/coordinator/coordinator-do';
import { PlatformTypes } from '../../src/ui/constants';
import { COORDINATOR_CONSTANTS } from '../../src/shared/coordinator-types';

describe('CoordinatorDO Integration Tests', () => {
  let coordinator: DurableObjectStub<CoordinatorDO>;
  let testCounter = 0;

  beforeEach(async () => {
    // Create a unique CoordinatorDO instance for each test to avoid state conflicts
    testCounter++;
    const coordinatorId = env.CoordinatorDO.idFromName(`test-coordinator-${testCounter}`);
    coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;
  });

  afterEach(() => {
    // Reset system time after each test
    vi.useRealTimers();
  });

  describe('Link Creation', () => {
    it('should create a new link with correct format and scheduling', async () => {
      // Arrange
      const platform: PlatformTypes = 'facebook';
      const userId = 'test-user';

      // Act
      const result = await coordinator.createLink(platform, userId);

      // Assert - Basic link properties
      expect(result).toBeDefined();
      expect(result.linkId).toBeDefined();
      expect(result.url).toBeDefined();
      expect(result.expiresAt).toBeDefined();

      // Assert - URL format validation
      expect(result.url).toMatch(
        `${env.KAKU_API_ENDPOINT}/connections/${result.linkId}/${platform}?userId=${userId}`,
      );
      expect(result.url).toContain(result.linkId);

      // Assert - Expiration timing (24 hours)
      const now = Date.now();
      const expectedExpiration = now + COORDINATOR_CONSTANTS.LINK_EXPIRATION_TIME;
      const timeDifference = Math.abs(result.expiresAt - expectedExpiration);
      expect(timeDifference).toBeLessThan(1000); // Within 1 second tolerance

      // Assert - Scheduled tasks created
      const schedules = (await coordinator.getSchedules()) as any[];
      expect(schedules).toBeDefined();
      expect(schedules.length).toBe(2);

      // Find expiration and deletion schedules
      const expirationSchedule = schedules.find((s: any) => s.callback === 'handleLinkExpiration');
      const deletionSchedule = schedules.find((s: any) => s.callback === 'deleteAgentDO');

      expect(expirationSchedule).toBeDefined();
      expect(expirationSchedule?.payload).toBe(result.linkId);

      expect(deletionSchedule).toBeDefined();
      expect(deletionSchedule?.payload).toBe(result.linkId);

      // Assert - Timing relationships
      expect(expirationSchedule!.time).toBeLessThan(deletionSchedule!.time);
    });

    it('should prevent duplicate links for already connected platform', async () => {
      // Arrange
      const platform: PlatformTypes = 'google';
      const userId = 'u_user123';

      // Act - Create first link
      const firstLink = await coordinator.createLink(platform, userId);
      expect(firstLink).toBeDefined();

      // Mark platform as connected
      await coordinator.markPlatformConnected(firstLink.linkId);

      // Act & Assert - Attempt to create second link should fail
      await expect(coordinator.createLink(platform, userId)).rejects.toThrow(
        'User is already connected to google',
      );
    });

    it('should enforce retry limits per platform', async () => {
      // Arrange
      const platform: PlatformTypes = 'netflix';
      const userId = 'u_userRetry';

      // Act - Create initial link
      const initialLink = await coordinator.createLink(platform, userId);

      // Exhaust retry attempts (PLATFORM_RETRY_LIMIT = 3)
      let currentLinkId = initialLink.linkId;
      for (let i = 0; i < 3; i++) {
        const resetResult = await coordinator.resetLink(currentLinkId, userId);
        if (resetResult) {
          currentLinkId = resetResult.linkInfo.linkId;
        }
      }

      // Act & Assert - Next retry should fail
      await expect(coordinator.resetLink(currentLinkId, userId)).rejects.toThrow(
        'Retry limit exceeded for netflix',
      );
    });

    it('should not remove the old link when resetting', async () => {
      // Arrange
      const platform: PlatformTypes = 'netflix';
      const userId = 'u_userReset123';
      const initialLink = await coordinator.createLink(platform, userId);

      // Act - Reset the link
      await coordinator.resetLink(initialLink.linkId, userId);

      // There should be two links now
      const coordinatorState = (await coordinator.state) as any;
      const platformData = coordinatorState.platforms.find((p: any) => p.id === platform);
      expect(platformData?.links.length).toBe(2);

      // Assert - The old link should still exists but with the status expired
      const linkStatus = await coordinator.getStatus(initialLink.linkId);
      expect(linkStatus?.status).toBe('expired');
    });
  });

  describe('Scheduling and Expiration', () => {
    it('should trigger link expiration after 24 hours', async () => {
      // Arrange
      const platform: PlatformTypes = 'github';
      const userId = 'u_schedule_user';

      // Act - Create link
      const result = await coordinator.createLink(platform, userId);
      const schedules = (await coordinator.getSchedules()) as any[];
      const expirationSchedule = schedules.find((s: any) => s.callback === 'handleLinkExpiration');

      expect(expirationSchedule).toBeDefined();
      expect(expirationSchedule.payload).toBe(result.linkId);

      // Verify expiration timing is approximately 24 hours
      const expectedExpirationTime = Date.now() + COORDINATOR_CONSTANTS.LINK_EXPIRATION_TIME;
      const timeDifference = Math.abs(expirationSchedule.time * 1000 - expectedExpirationTime);
      expect(timeDifference).toBeLessThan(2000); // Within 2 seconds tolerance

      // Simulate time passing to expiration time
      vi.setSystemTime(expirationSchedule.time * 1000 + 1000);

      // Trigger scheduled callback
      const success = await runDurableObjectAlarm(coordinator);

      // Assert - The alarm should execute successfully
      expect(success).toBe(true);

      // Verify the link status changed to expired by checking getStatus
      const linkStatus = await coordinator.getStatus(result.linkId);
      expect(linkStatus?.status).toBe('expired');
    });

    it('should schedule agent deletion after expiration', async () => {
      // Arrange
      const platform: PlatformTypes = 'test';
      const userId = 'u_deletion-test';

      // Act - Create link and get schedules
      const result = await coordinator.createLink(platform, userId);
      const schedules = (await coordinator.getSchedules()) as any[];

      const deletionSchedule = schedules.find((s: any) => s.callback === 'deleteAgentDO');
      expect(deletionSchedule).toBeDefined();
      expect(deletionSchedule.payload).toBe(result.linkId);

      // Verify deletion is scheduled for 7 days after creation
      const expectedDeletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
      const timeDifference = Math.abs(deletionSchedule!.time * 1000 - expectedDeletionTime);
      expect(timeDifference).toBeLessThan(2000); // Within 2 seconds tolerance

      // Verify that both expiration and deletion are scheduled
      const expirationSchedule = schedules.find((s: any) => s.callback === 'handleLinkExpiration');
      expect(expirationSchedule).toBeDefined();

      // Deletion should be scheduled after expiration
      expect(deletionSchedule.time).toBeGreaterThan(expirationSchedule.time);

      // Simulate time passing to deletion time
      vi.setSystemTime(deletionSchedule.time * 1000 + 1000);

      // Trigger scheduled callback
      const success = await runDurableObjectAlarm(coordinator);

      // Assert - The alarm should execute successfully
      expect(success).toBe(true);
    });
  });

  describe('Agent DO Management', () => {
    it('should create and track agent DO for new link', async () => {
      // Arrange
      const platform: PlatformTypes = 'kazeel';
      const userId = 'u_userIdkazeel';

      // Act
      const result = await coordinator.createLink(platform, userId);

      // Get the agent ID using the RPC method
      const agentId = await coordinator.getAgentIdForLink(result.linkId);

      // Assert
      expect(agentId).toBeDefined();
      expect(agentId).toBe(result.linkId); // Agent ID should match link ID
    });

    it('should handle agent DO deletion gracefully', async () => {
      // Arrange
      const agentId = 'test-agent-123';

      // Act & Assert - Should not throw error even if agent doesn't exist
      await expect(coordinator.deleteAgentDO(agentId)).resolves.not.toThrow();
    });

    it('should verify agent DO creation during link creation', async () => {
      // Arrange
      const platform: PlatformTypes = 'github';
      const userId = 'u_agent-creation-test';

      // Act
      const result = await coordinator.createLink(platform, userId);

      // Assert - Verify schedules include agent deletion
      const schedules = (await coordinator.getSchedules()) as any[];
      const deletionSchedule = schedules.find((s: any) => s.callback === 'deleteAgentDO');

      expect(deletionSchedule).toBeDefined();
      expect(deletionSchedule.payload).toBe(result.linkId);

      // Verify the agent ID can be retrieved
      const agentId = await coordinator.getAgentIdForLink(result.linkId);
      expect(agentId).toBe(result.linkId);
    });
  });

  describe('Platform Connections', () => {
    it('should return all platforms with default disconnected state', async () => {
      // Act
      const result = await coordinator.getPlatformConnections();

      // Assert
      expect(result).toBeDefined();
      expect(result.connections).toBeDefined();
      expect(Array.isArray(result.connections)).toBe(true);
      expect(result.connections.length).toBeGreaterThan(0);

      // Verify each connection has required properties
      result.connections.forEach((connection) => {
        expect(connection.id).toBeDefined();
        expect(connection.name).toBeDefined();
        expect(connection.logo).toBeDefined();
        expect(typeof connection.connected).toBe('boolean');
        expect(connection.connected).toBe(false); // Should be false initially
      });

      // Verify specific platforms are included
      const platformIds = result.connections.map((c) => c.id);
      expect(platformIds).toContain('facebook');
      expect(platformIds).toContain('netflix');
      expect(platformIds).toContain('github');
      expect(platformIds).toContain('google');
    });

    it('should show connected state for platforms with active connections', async () => {
      // Arrange - Create and connect a platform
      const platform: PlatformTypes = 'facebook';
      const userId = 'u_connected-user';

      const linkResult = await coordinator.createLink(platform, userId);
      await coordinator.markPlatformConnected(linkResult.linkId);

      // Act
      const result = await coordinator.getPlatformConnections();

      // Assert
      const facebookConnection = result.connections.find((c) => c.id === 'facebook');
      expect(facebookConnection).toBeDefined();
      expect(facebookConnection?.connected).toBe(true);

      // Other platforms should still be disconnected
      const otherConnections = result.connections.filter((c) => c.id !== 'facebook');
      otherConnections.forEach((connection) => {
        expect(connection.connected).toBe(false);
      });
    });

    it('should maintain connection state across multiple platforms', async () => {
      // Arrange - Connect multiple platforms
      const userId = 'u_multi-platform-user';
      const platforms: PlatformTypes[] = ['facebook', 'github'];

      for (const platform of platforms) {
        const linkResult = await coordinator.createLink(platform, userId);
        await coordinator.markPlatformConnected(linkResult.linkId);
      }

      // Act
      const result = await coordinator.getPlatformConnections();

      // Assert
      const connectedPlatforms = result.connections.filter((c) => c.connected);
      expect(connectedPlatforms.length).toBe(2);

      const connectedIds = connectedPlatforms.map((c) => c.id);
      expect(connectedIds).toContain('facebook');
      expect(connectedIds).toContain('github');

      // Verify disconnected platforms
      const disconnectedPlatforms = result.connections.filter((c) => !c.connected);
      expect(disconnectedPlatforms.length).toBeGreaterThan(0);
    });

    it('should disconnect a connected platform', async () => {
      // Arrange - Connect a platform
      const platform: PlatformTypes = 'facebook';
      const userId = 'u_disconnect-user';

      const linkResult = await coordinator.createLink(platform, userId);
      await coordinator.markPlatformConnected(linkResult.linkId);

      // Verify platform is connected
      let result = await coordinator.getPlatformConnections();
      let facebookConnection = result.connections.find((c) => c.id === 'facebook');
      expect(facebookConnection?.connected).toBe(true);

      // Act - Disconnect the platform
      const disconnected = await coordinator.disconnectPlatform(platform);

      // Assert
      expect(disconnected).toBe(true);

      // Verify platform is now disconnected
      result = await coordinator.getPlatformConnections();
      facebookConnection = result.connections.find((c) => c.id === 'facebook');
      expect(facebookConnection?.connected).toBe(false);

      // Verify link status is expired
      const linkStatus = await coordinator.getStatus(linkResult.linkId);
      expect(linkStatus?.status).toBe('expired');
    });

    it('should return false when disconnecting non-connected platform', async () => {
      // Arrange - Platform not connected
      const platform: PlatformTypes = 'netflix';

      // Act - Attempt to disconnect
      const disconnected = await coordinator.disconnectPlatform(platform);

      // Assert
      expect(disconnected).toBe(false);
    });

    it('should reset retry count when disconnecting platform', async () => {
      // Arrange - Connect a platform and increment retry count
      const platform: PlatformTypes = 'google';
      const userId = 'u_retry-reset-user';

      const linkResult = await coordinator.createLink(platform, userId);

      // Simulate some retries by resetting the link
      await coordinator.resetLink(linkResult.linkId, userId);

      await coordinator.markPlatformConnected(linkResult.linkId);

      // Verify retry count is incremented
      const coordinatorState = (await coordinator.state) as any;
      let platformData = coordinatorState.platforms.find((p: any) => p.id === platform);
      expect(platformData?.retryCount).toBeGreaterThan(0);

      // Act - Disconnect the platform
      await coordinator.disconnectPlatform(platform);

      // Assert - Retry count should be reset
      const updatedState = (await coordinator.state) as any;
      platformData = updatedState.platforms.find((p: any) => p.id === platform);
      expect(platformData?.retryCount).toBe(0);
    });

    it('should return consistent platform metadata', async () => {
      // Act
      const result = await coordinator.getPlatformConnections();

      // Assert - Verify platform details match constants
      const facebookConnection = result.connections.find((c) => c.id === 'facebook');
      expect(facebookConnection).toBeDefined();
      expect(facebookConnection?.name).toBe('Facebook');
      expect(facebookConnection?.logo).toBe('/fb.png');

      const netflixConnection = result.connections.find((c) => c.id === 'netflix');
      expect(netflixConnection).toBeDefined();
      expect(netflixConnection?.name).toBe('Netflix');
      expect(netflixConnection?.logo).toBe('/netflix.png');

      const githubConnection = result.connections.find((c) => c.id === 'github');
      expect(githubConnection).toBeDefined();
      expect(githubConnection?.name).toBe('Github');
      expect(githubConnection?.logo).toBe('/github.png');
    });
  });
});
