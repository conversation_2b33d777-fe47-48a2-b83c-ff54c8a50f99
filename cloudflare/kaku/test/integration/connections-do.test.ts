import { env, runDurableObjectAlarm } from 'cloudflare:test';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AgentState } from '../../src/agent/types';

describe('Connections DO Integration Tests', () => {
  let testCounter = 0;

  beforeEach(() => {
    testCounter++;
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should setup and destroy the DO', async () => {
    // Arrange
    const connectionsId = env.Connections.idFromName(`test-connection-${testCounter}`);
    const stub = env.Connections.get(connectionsId);

    await stub.setup('facebook', 'u_userId', `test-connection-${testCounter}`);

    const initialState = (await stub.state) as AgentState;

    // Assert initial state
    expect(initialState).toBeDefined();
    expect(initialState.platformId).toBe('facebook');
    expect(initialState.userId).toBe('u_userId');
    expect(initialState.referenceId).toBe(`test-connection-${testCounter}`);
    expect(initialState.status).toBe('initial');

    // Act - Delete all data
    await expect(stub.deleteAll()).resolves.not.toThrow();
  });

  it('should schedule a cleanup event when setup is called', async () => {
    // Arrange
    const connectionsId = env.Connections.idFromName(`test-schedule-${testCounter}`);
    const stub = env.Connections.get(connectionsId);

    // Act - Setup the connection (this should schedule cleanup)
    await stub.setup('facebook', 'u_userId', `test-schedule-${testCounter}`);

    // Get the scheduled tasks
    const schedules = (await stub.getSchedules()) as any[];

    // Assert - Should have one scheduled task
    expect(schedules).toBeDefined();
    expect(schedules.length).toBe(1);
    expect(schedules[0].callback).toBe('deleteAll');
    expect(schedules[0].time).toBeGreaterThanOrEqual(Date.now() / 1000);

    vi.setSystemTime(schedules[0].time * 1000 + 1000);
    const success = await runDurableObjectAlarm(stub);
    expect(success).toBe(true);

    // throws error since it does not have a table
    let flag = false;
    try {
      const sch = await stub.getSchedules();
      flag = true;
    } catch (error) {}
    expect(flag).toBe(false);
  });

  it('should handle state updates correctly', async () => {
    // Arrange
    const connectionsId = env.Connections.idFromName(`test-state-${testCounter}`);
    const stub = env.Connections.get(connectionsId);

    // Act - Setup the connection
    await stub.setup('github', 'u_testUser', `test-state-${testCounter}`);

    const state = (await stub.state) as AgentState;

    expect(state.platformId).toBe('github');
    expect(state.userId).toBe('u_testUser');
    expect(state.referenceId).toBe(`test-state-${testCounter}`);
    expect(state.status).toBe('initial');
    expect(state.captchaSetupComplete).toBe(false);
    expect(state.initializationStatus).toBe('initial');
    expect(state.termsAndConditionsApproved).toBe(false);
  });
});
