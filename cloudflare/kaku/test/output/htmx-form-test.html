<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX Form Generator Test - 2-Step Verification</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <link rel="stylesheet" href="../../public/css/styles.css" />
    <link rel="stylesheet" href="../../public/css/video-layout.css" />
    <link rel="stylesheet" href="../../public/css/bottom-sheet.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div id="connection-flow-wrapper" class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
            <div id="connection-flow" class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
                <div class="text-start w-full">
                    <h1 class="text-xl font-semibold mb-4">2-Step Verification</h1>
                    <p class="text-base mb-2">Google sent a notification to your iPhone. Open the Gmail app and tap Yes on the prompt to verify it's you.</p>
                </div>
                <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission"}'>
<div class="prompt-container">
      <div class="contextual-info">
        <div class="contextual-info-text">Open the Gmail app on iPhone
Google sent a notification to your iPhone. Open the Gmail app and tap Yes on the prompt to verify it's you.</div>
      </div>
    </div>

    <div class="input-container">
      <div class="checkbox-container">
        <input
          class="checkbox-field"
          type="checkbox"
          id="dont_ask_on_this_device_checkbox"
          name="dont_ask_on_this_device_checkbox"
        >
        <label class="checkbox-label" for="dont_ask_on_this_device_checkbox">Don't ask again on this device</label>
      </div>
    </div>
  

    <div class="button-container">
      <button
        type="button"
        class="button-link"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click" hx-vals='{"type": "form_submission", "clickId": "resend_it_button", "interaction": "click"}'
      >
        Resend it
      </button>
    </div>
  

    <div class="button-container">
      <button
        type="button"
        class="button-link"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click" hx-vals='{"type": "form_submission", "clickId": "try_another_way_button", "interaction": "click"}'
      >
        Try another way
      </button>
    </div>
  
</form>

               <div style="margin-top: 2rem; padding: 1rem; background-color: #f0f0f0; border-radius: 0.5rem; width: 100%;">
  <h3 style="font-size: 0.875rem; font-weight: 600; color: #1c1b1f; margin-bottom: 0.5rem;">
    Form Analysis Data (Simplified Schema):
  </h3>
  <details style="font-size: 0.75rem;">
    <summary style="cursor: pointer; color: #6750a4; margin-bottom: 0.5rem; transition: color 0.2s;"
             onmouseover="this.style.color='#5f4da5'"
             onmouseout="this.style.color='#6750a4'">
      View Raw JSON
    </summary>
    <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: #ffffff; border: 1px solid #cac4d0; border-radius: 0.25rem; font-size: 0.75rem; overflow: auto; max-height: 10rem; color: #1c1b1f;">
      {
  "extractionResult": {
    "screenInfo": {
      "authState": "not-authenticated",
      "errors": [],
      "title": "2-Step Verification",
      "controlVisibilityRules": [
        {
          "id": "email_field",
          "status": "included",
          "reason": "This is the user's email address, which is a required field for authentication."
        },
        {
          "id": "dont_ask_on_this_device_checkbox",
          "status": "included",
          "reason": "This checkbox allows the user to manage their login preferences, which is relevant to the authentication flow."
        },
        {
          "id": "resend_it_button",
          "status": "included",
          "reason": "This button allows the user to resend the verification code, which is part of the authentication process."
        },
        {
          "id": "try_another_way_button",
          "status": "included",
          "reason": "This button allows the user to choose an alternative verification method, which is part of the authentication flow."
        }
      ],
      "description": "To help keep your account safe, Google wants to make sure it's really you trying to sign in",
      "instruction": "Open the Gmail app on iPhone\nGoogle sent a notification to your iPhone. Open the Gmail app and tap Yes on the prompt to verify it's you."
    },
    "controls": {
      "fields": [
        {
          "isLikelyDropdownReason": "The control has a dropdown arrow, indicating it is a dropdown field.",
          "id": "email_field",
          "order": 1,
          "label": "<EMAIL>",
          "fieldControlType": "dropdown",
          "actiontype": "select",
          "name": "email_field",
          "checked": false,
          "options": []
        },
        {
          "isLikelyDropdownReason": "The control is a checkbox.",
          "id": "dont_ask_on_this_device_checkbox",
          "order": 2,
          "label": "Don't ask again on this device",
          "fieldControlType": "checkbox",
          "actiontype": "fill",
          "name": "dont_ask_on_this_device_checkbox",
          "checked": true,
          "options": []
        }
      ],
      "buttons": [
        {
          "id": "resend_it_button",
          "order": 3,
          "label": "Resend it",
          "variant": "link",
          "type": "click",
          "actiontype": "click"
        },
        {
          "id": "try_another_way_button",
          "order": 4,
          "label": "Try another way",
          "variant": "link",
          "type": "click",
          "actiontype": "click"
        }
      ]
    }
  },
  "classificationResult": {
    "screenInfo": {
      "classificationReasoning": "The screen displays a prompt for '2-Step Verification' and instructs the user to open their Gmail app on an iPhone to verify their identity, which is a common multi-factor authentication step.",
      "screenClass": "multi-factor-verification-screen",
      "description": "Google sent a notification to your iPhone. Open the Gmail app and tap Yes on the prompt to verify it's you.",
      "title": "2-Step Verification",
      "authState": "not-authenticated",
      "errors": null,
      "verificationCode": null
    }
  }
}
    </pre>
  </details>
</div>

            </div>
        </div>
    </div>
</body>
</html>